import React from 'react';
import { Typo<PERSON>, Box, Card, CardContent, Button, Grid } from '@mui/material';
import { useTranslation } from 'react-i18next';
import AppPage from '../components/AppPage';
import { DashboardProps } from '../types';

const SamplePage: React.FC<DashboardProps> = ({ user, onLogout }) => {
  const { t } = useTranslation();

  return (
    <AppPage user={user} onLogout={onLogout} title="Sample Page">
      <Box>
        <Typography variant="h4" gutterBottom>
          Sample Page
        </Typography>
        <Typography variant="body1" color="text.secondary" paragraph>
          This is a sample page to demonstrate the navigation system.
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Sample Card 1
                </Typography>
                <Typography variant="body2" paragraph>
                  This is a sample card with some content to show how the layout works.
                </Typography>
                <Button variant="contained" color="primary">
                  Sample Action
                </Button>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Sample Card 2
                </Typography>
                <Typography variant="body2" paragraph>
                  Another sample card to demonstrate the grid layout.
                </Typography>
                <Button variant="outlined" color="secondary">
                  Another Action
                </Button>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    </AppPage>
  );
};

export default SamplePage;
